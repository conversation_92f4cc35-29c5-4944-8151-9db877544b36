"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"5f6df70c0950\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/YmJmMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjVmNmRmNzBjMDk1MFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: function() { return /* binding */ Providers; },\n/* harmony export */   useLanguage: function() { return /* binding */ useLanguage; },\n/* harmony export */   useTheme: function() { return /* binding */ useTheme; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useTheme,useLanguage,Providers auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    theme: \"light\",\n    toggleTheme: ()=>{}\n});\nconst useTheme = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (!context) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n};\n_s(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    language: \"en\",\n    setLanguage: ()=>{},\n    t: (key)=>key\n});\nconst useLanguage = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (!context) {\n        throw new Error(\"useLanguage must be used within a LanguageProvider\");\n    }\n    return context;\n};\n_s1(useLanguage, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Simple translations\nconst translations = {\n    en: {\n        \"members\": \"Members\",\n        \"dashboard\": \"Dashboard\",\n        \"pos\": \"Point of Sale\",\n        \"inventory\": \"Inventory\",\n        \"sports\": \"Sports\",\n        \"classes\": \"Classes\",\n        \"reports\": \"Reports\",\n        \"settings\": \"Settings\",\n        \"logout\": \"Logout\",\n        \"add_member\": \"Add Member\",\n        \"edit_member\": \"Edit Member\",\n        \"delete_member\": \"Delete Member\",\n        \"save\": \"Save\",\n        \"cancel\": \"Cancel\",\n        \"name\": \"Name\",\n        \"email\": \"Email\",\n        \"phone\": \"Phone\",\n        \"category\": \"Category\",\n        \"manage_sports\": \"Manage Sports\",\n        \"manage_sports_description\": \"Add, edit, or delete sports categories for your gym management system\",\n        \"edit_sport\": \"Edit Sport\",\n        \"member_updated\": \"Member Updated\",\n        \"add_new_sport\": \"Add New Sport\",\n        \"gender\": \"Gender\",\n        \"male\": \"Male\",\n        \"female\": \"Female\",\n        \"sport\": \"Sport\",\n        \"view_details\": \"View Details\",\n        \"print_report\": \"Print Report\",\n        \"renew_subscription\": \"Renew Subscription\"\n    },\n    fr: {\n        \"members\": \"Membres\",\n        \"dashboard\": \"Tableau de bord\",\n        \"pos\": \"Point de vente\",\n        \"inventory\": \"Inventaire\",\n        \"sports\": \"Sports\",\n        \"classes\": \"Cours\",\n        \"reports\": \"Rapports\",\n        \"settings\": \"Param\\xe8tres\",\n        \"logout\": \"D\\xe9connexion\",\n        \"add_member\": \"Ajouter un membre\",\n        \"edit_member\": \"Modifier le membre\",\n        \"delete_member\": \"Supprimer le membre\",\n        \"save\": \"Enregistrer\",\n        \"cancel\": \"Annuler\",\n        \"name\": \"Nom\",\n        \"email\": \"Email\",\n        \"phone\": \"T\\xe9l\\xe9phone\",\n        \"category\": \"Cat\\xe9gorie\",\n        \"manage_sports\": \"G\\xe9rer les Sports\",\n        \"manage_sports_description\": \"Ajouter, modifier ou supprimer les cat\\xe9gories de sports pour votre syst\\xe8me de gestion de salle de sport\",\n        \"edit_sport\": \"Modifier le Sport\",\n        \"member_updated\": \"Membre Mis \\xe0 Jour\",\n        \"add_new_sport\": \"Ajouter un Nouveau Sport\",\n        \"gender\": \"Genre\",\n        \"male\": \"Homme\",\n        \"female\": \"Femme\",\n        \"sport\": \"Sport\"\n    },\n    ar: {\n        \"members\": \"الأعضاء\",\n        \"dashboard\": \"لوحة التحكم\",\n        \"pos\": \"نقطة البيع\",\n        \"inventory\": \"المخزون\",\n        \"sports\": \"الرياضات\",\n        \"classes\": \"الفصول\",\n        \"reports\": \"التقارير\",\n        \"settings\": \"الإعدادات\",\n        \"logout\": \"تسجيل الخروج\",\n        \"add_member\": \"إضافة عضو\",\n        \"edit_member\": \"تعديل العضو\",\n        \"delete_member\": \"حذف العضو\",\n        \"save\": \"حفظ\",\n        \"cancel\": \"إلغاء\",\n        \"name\": \"الاسم\",\n        \"email\": \"البريد الإلكتروني\",\n        \"phone\": \"الهاتف\",\n        \"category\": \"الفئة\",\n        \"manage_sports\": \"إدارة الرياضات\",\n        \"manage_sports_description\": \"إضافة وتعديل وحذف فئات الرياضة لنظام إدارة النادي\",\n        \"edit_sport\": \"تعديل الرياضة\",\n        \"member_updated\": \"تم تحديث العضو\",\n        \"add_new_sport\": \"إضافة رياضة جديدة\",\n        \"gender\": \"الجنس\",\n        \"male\": \"ذكر\",\n        \"female\": \"أنثى\",\n        \"sport\": \"الرياضة\"\n    }\n};\nfunction Providers(param) {\n    let { children } = param;\n    _s2();\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"en\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Load theme from localStorage\n        const savedTheme = localStorage.getItem(\"gym-theme\");\n        if (savedTheme) {\n            setTheme(savedTheme);\n            document.documentElement.classList.toggle(\"dark\", savedTheme === \"dark\");\n        }\n        // Load language from localStorage\n        const savedLanguage = localStorage.getItem(\"gym-language\");\n        if (savedLanguage) {\n            setLanguage(savedLanguage);\n            document.documentElement.setAttribute(\"lang\", savedLanguage);\n            document.documentElement.setAttribute(\"dir\", savedLanguage === \"ar\" ? \"rtl\" : \"ltr\");\n        }\n    }, []);\n    const toggleTheme = ()=>{\n        const newTheme = theme === \"light\" ? \"dark\" : \"light\";\n        setTheme(newTheme);\n        localStorage.setItem(\"gym-theme\", newTheme);\n        document.documentElement.classList.toggle(\"dark\", newTheme === \"dark\");\n    };\n    const handleSetLanguage = (lang)=>{\n        setLanguage(lang);\n        localStorage.setItem(\"gym-language\", lang);\n        document.documentElement.setAttribute(\"lang\", lang);\n        document.documentElement.setAttribute(\"dir\", lang === \"ar\" ? \"rtl\" : \"ltr\");\n    };\n    const t = (key)=>{\n        var _translations_language;\n        return ((_translations_language = translations[language]) === null || _translations_language === void 0 ? void 0 : _translations_language[key]) || key;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n            value: {\n                language,\n                setLanguage: handleSetLanguage,\n                t\n            },\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\providers.tsx\",\n            lineNumber: 180,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\providers.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, this);\n}\n_s2(Providers, \"v7iOU9QEBMOWMB4qqRVdgEHG+tI=\");\n_c = Providers;\nvar _c;\n$RefreshReg$(_c, \"Providers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers.tsx\n"));

/***/ })

});