'use client'

import { useState, useEffect, useRef } from 'react'
import { Html5QrcodeScanner, Html5QrcodeScannerConfig, Html5QrcodeScanType } from 'html5-qrcode'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useLanguage } from '@/components/providers'
import { useToast } from '@/hooks/use-toast'
import {
  X,
  Camera,
  ScanBarcode,
  QrCode,
  RotateCcw,
  AlertCircle,
} from 'lucide-react'

interface ScannerModalProps {
  isOpen: boolean
  onClose: () => void
  onScanSuccess: (result: string) => void
  scanType?: 'qr' | 'barcode' | 'both'
}

export function ScannerModal({ 
  isOpen, 
  onClose, 
  onScanSuccess, 
  scanType = 'both' 
}: ScannerModalProps) {
  const [isScanning, setIsScanning] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [cameraPermission, setCameraPermission] = useState<'granted' | 'denied' | 'prompt'>('prompt')
  const scannerRef = useRef<Html5QrcodeScanner | null>(null)
  const { t } = useLanguage()
  const { toast } = useToast()

  useEffect(() => {
    if (isOpen) {
      checkCameraPermission()
    }
    return () => {
      stopScanning()
    }
  }, [isOpen])

  const checkCameraPermission = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true })
      setCameraPermission('granted')
      stream.getTracks().forEach(track => track.stop())
    } catch (error) {
      setCameraPermission('denied')
      setError(t('camera_permission'))
    }
  }

  const startScanning = () => {
    if (scannerRef.current) {
      stopScanning()
    }

    const config: Html5QrcodeScannerConfig = {
      fps: 10,
      qrbox: { width: 250, height: 250 },
      aspectRatio: 1.0,
      disableFlip: false,
      supportedScanTypes: getScanTypes(),
    }

    const scanner = new Html5QrcodeScanner('qr-reader', config, false)
    scannerRef.current = scanner

    scanner.render(
      (decodedText: string) => {
        onScanSuccess(decodedText)
        stopScanning()
        onClose()
        toast({
          title: t('scan_result'),
          description: `${t('code_not_found')}: ${decodedText}`,
        })
      },
      (error: any) => {
        // Handle scan errors silently - they're frequent during scanning
        console.debug('Scan error:', error)
      }
    )

    setIsScanning(true)
    setError(null)
  }

  const stopScanning = () => {
    if (scannerRef.current) {
      try {
        scannerRef.current.clear()
      } catch (error) {
        console.debug('Error clearing scanner:', error)
      }
      scannerRef.current = null
    }
    setIsScanning(false)
  }

  const getScanTypes = (): Html5QrcodeScanType[] => {
    switch (scanType) {
      case 'qr':
        return [Html5QrcodeScanType.SCAN_TYPE_CAMERA]
      case 'barcode':
        return [Html5QrcodeScanType.SCAN_TYPE_CAMERA]
      case 'both':
      default:
        return [Html5QrcodeScanType.SCAN_TYPE_CAMERA]
    }
  }

  const handleClose = () => {
    stopScanning()
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center space-x-2">
              {scanType === 'qr' && <QrCode className="w-5 h-5" />}
              {scanType === 'barcode' && <ScanBarcode className="w-5 h-5" />}
              {scanType === 'both' && <Camera className="w-5 h-5" />}
              <span>
                {scanType === 'qr' && t('scan_qr_code')}
                {scanType === 'barcode' && t('scan_barcode')}
                {scanType === 'both' && t('scan_qr_code')}
              </span>
            </span>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleClose}
            >
              <X className="w-4 h-4" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {cameraPermission === 'denied' && (
            <div className="flex items-center space-x-2 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <AlertCircle className="w-5 h-5 text-red-500" />
              <p className="text-sm text-red-700 dark:text-red-300">
                {t('camera_permission')}
              </p>
            </div>
          )}

          {cameraPermission === 'granted' && !isScanning && (
            <div className="text-center space-y-4">
              <div className="w-full h-48 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                <Camera className="w-12 h-12 text-gray-400" />
              </div>
              <Button
                variant="gym"
                className="w-full"
                onClick={startScanning}
              >
                <Camera className="w-4 h-4 mr-2" />
                {t('start_scanning')}
              </Button>
            </div>
          )}

          {isScanning && (
            <div className="space-y-4">
              <div id="qr-reader" className="w-full"></div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={stopScanning}
                >
                  {t('stop_scanning')}
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => {
                    stopScanning()
                    setTimeout(startScanning, 100)
                  }}
                >
                  <RotateCcw className="w-4 h-4" />
                </Button>
              </div>
            </div>
          )}

          {error && (
            <div className="flex items-center space-x-2 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <AlertCircle className="w-5 h-5 text-red-500" />
              <p className="text-sm text-red-700 dark:text-red-300">
                {error}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
